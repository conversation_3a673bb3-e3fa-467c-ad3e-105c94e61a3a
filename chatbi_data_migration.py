import subprocess
import datetime
import os
import argparse
import sys
import re

def process_insert_batches(sql_content, batch_size=1000):
    """将INSERT语句按指定批次大小重新组织"""
    lines = sql_content.split('\n')
    processed_lines = []
    current_insert = None
    current_values = []

    for line in lines:
        line = line.strip()

        # 跳过空行和注释
        if not line or line.startswith('--'):
            processed_lines.append(line)
            continue

        # 检测INSERT语句开始
        if line.startswith('INSERT INTO'):
            # 如果有之前的INSERT，先处理完
            if current_insert and current_values:
                processed_lines.extend(create_batched_inserts(current_insert, current_values, batch_size))
                current_values = []

            # 解析新的INSERT语句
            if ' VALUES ' in line:
                # 单行INSERT语句
                parts = line.split(' VALUES ', 1)
                current_insert = parts[0] + ' VALUES '
                values_part = parts[1].rstrip(';')
                current_values.extend(parse_values(values_part))
            else:
                # 多行INSERT语句开始
                current_insert = line + ' '
        elif current_insert and line.startswith('(') and line.endswith(');'):
            # INSERT语句的VALUES部分结束
            values_part = line.rstrip(';')
            current_values.extend(parse_values(values_part))
            processed_lines.extend(create_batched_inserts(current_insert, current_values, batch_size))
            current_insert = None
            current_values = []
        elif current_insert:
            # INSERT语句的VALUES部分继续
            if line.endswith(';'):
                values_part = line.rstrip(';')
                current_values.extend(parse_values(values_part))
                processed_lines.extend(create_batched_inserts(current_insert, current_values, batch_size))
                current_insert = None
                current_values = []
            else:
                current_values.extend(parse_values(line))
        else:
            # 其他SQL语句
            processed_lines.append(line)

    # 处理最后一个INSERT
    if current_insert and current_values:
        processed_lines.extend(create_batched_inserts(current_insert, current_values, batch_size))

    return '\n'.join(processed_lines)

def parse_values(values_str):
    """解析VALUES字符串，提取各个值组"""
    values = []
    if not values_str.strip():
        return values

    # 简单的括号匹配来分割VALUES
    current_value = ""
    paren_count = 0
    in_string = False
    escape_next = False

    for char in values_str:
        if escape_next:
            current_value += char
            escape_next = False
            continue

        if char == '\\':
            escape_next = True
            current_value += char
            continue

        if char == "'" and not escape_next:
            in_string = not in_string

        if not in_string:
            if char == '(':
                paren_count += 1
            elif char == ')':
                paren_count -= 1

        current_value += char

        # 当括号平衡且遇到逗号时，说明一个值组结束
        if not in_string and paren_count == 0 and char == ')':
            if current_value.strip():
                values.append(current_value.strip())
            current_value = ""
            # 跳过后面的逗号和空格
            continue

    # 添加最后一个值组
    if current_value.strip():
        values.append(current_value.strip())

    return values

def create_batched_inserts(insert_prefix, values_list, batch_size):
    """创建批次化的INSERT语句"""
    batched_inserts = []

    for i in range(0, len(values_list), batch_size):
        batch = values_list[i:i + batch_size]
        if batch:
            insert_sql = insert_prefix + ','.join(batch) + ';'
            batched_inserts.append(insert_sql)

    return batched_inserts

def export_chatbi_data_only(database_name='chatbi_test'):
    """Export only data (INSERT statements) from specified database"""

    # Database configuration
    config = {
        'host': 'mysql-xm.summerfarm.net',
        'port': '3308',
        'user': 'test',
        'password': 'xianmu619',
        'database': database_name
    }
    
    # List of tables to export
    tables = [
        'bad_case',
        'chat_history', 
        'department_top_questions_weekly',
        'good_case',
        'share_map',
        'user_recommendation_offline',
        'user_session',
        'user'
    ]
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d")
    output_file = f"chatbi_data_only_{timestamp}.sql"

    # Build mysqldump command with data-only options and batch size
    cmd = [
        'mysqldump',
        '-h', config['host'],
        '-P', config['port'],
        '-u', config['user'],
        f"-p{config['password']}",
        '--no-create-info',      # Don't include CREATE TABLE statements
        '--skip-add-drop-table', # Don't include DROP TABLE statements
        '--skip-comments',       # Skip comments for cleaner output
        '--compact',             # Produce more compact output
        '--single-transaction',  # Ensure consistent backup
        '--extended-insert',     # Use extended INSERT syntax for better performance
        '--net_buffer_length=32K',  # Optimize network buffer
        config['database']
    ] + tables  # Add table names at the end
    
    try:
        print(f"Exporting data from database {config['database']}...")
        print(f"Host: {config['host']}:{config['port']}")
        print(f"Tables: {', '.join(tables)}")
        print("Options: Data only (no CREATE/DROP statements)")
        print("-" * 50)
        
        with open(output_file, 'w') as f:
            # Write header comment
            f.write(f"-- Data export for {database_name} database\n")
            f.write(f"-- Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"-- Contains INSERT statements only (no CREATE/DROP statements)\n")
            f.write(f"-- Batch size optimized for 1000 records per INSERT\n")
            f.write(f"--\n\n")

            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

            # Process the output to batch INSERT statements
            if result.returncode == 0:
                processed_output = process_insert_batches(result.stdout, batch_size=1000)
                f.write(processed_output)
        
        if result.returncode == 0:
            file_size = os.path.getsize(output_file)
            print(f"✅ Export successful!")
            print(f"📁 File: {output_file}")
            print(f"📊 Size: {file_size / 1024:.2f} KB")
            
            # Show preview of file contents
            print("\n📋 File preview (first 10 lines):")
            with open(output_file, 'r') as f:
                for i, line in enumerate(f):
                    if i >= 10:
                        break
                    print(f"   {line.rstrip()}")
            
            print(f"\n💡 To import this data:")
            print(f"   mysql -h your_host -P your_port -u your_user -p your_database < {output_file}")
            
        else:
            print(f"❌ Export failed!")
            print(f"Error: {result.stderr}")
            if os.path.exists(output_file):
                os.remove(output_file)
                
    except FileNotFoundError:
        print("❌ Error: mysqldump command not found!")
        print("Please make sure MySQL client tools are installed.")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def export_specific_tables(table_list, database_name='chatbi_test'):
    """Export data from specific tables only"""

    config = {
        'host': 'mysql-xm.summerfarm.net',
        'port': '3308',
        'user': 'test',
        'password': 'xianmu619',
        'database': database_name
    }
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"chatbi_selected_data_{timestamp}.sql"
    
    cmd = [
        'mysqldump',
        '-h', config['host'],
        '-P', config['port'],
        '-u', config['user'],
        f"-p{config['password']}",
        '--no-create-info',
        '--skip-add-drop-table',
        '--skip-comments',
        '--compact',
        '--single-transaction',
        '--extended-insert',     # Use extended INSERT syntax for better performance
        '--net_buffer_length=32K',  # Optimize network buffer
        config['database']
    ] + table_list
    
    try:
        print(f"Exporting data from selected tables: {', '.join(table_list)}")
        
        with open(output_file, 'w') as f:
            f.write(f"-- Selected tables data export from {database_name} database\n")
            f.write(f"-- Tables: {', '.join(table_list)}\n")
            f.write(f"-- Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"-- Batch size optimized for 1000 records per INSERT\n\n")

            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

            # Process the output to batch INSERT statements
            if result.returncode == 0:
                processed_output = process_insert_batches(result.stdout, batch_size=1000)
                f.write(processed_output)
        
        if result.returncode == 0:
            file_size = os.path.getsize(output_file)
            print(f"✅ Export successful! File: {output_file} ({file_size / 1024:.2f} KB)")
        else:
            print(f"❌ Export failed: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """主函数，处理命令行参数"""
    parser = argparse.ArgumentParser(description='ChatBI数据导出工具')
    parser.add_argument('-d', '--database',
                       default='chatbi_test',
                       help='数据库名称 (默认: chatbi_test)')
    parser.add_argument('-t', '--tables',
                       help='指定要导出的表名，用逗号分隔 (例如: user,chat_history)')
    parser.add_argument('--interactive',
                       action='store_true',
                       help='使用交互式模式')

    args = parser.parse_args()

    print("🚀 ChatBI Data Export Tool")
    print("=" * 40)
    print(f"数据库: {args.database}")

    if args.tables:
        # 命令行指定表名模式
        table_list = [table.strip() for table in args.tables.split(',')]
        print(f"导出表: {', '.join(table_list)}")
        export_specific_tables(table_list, args.database)
    elif args.interactive:
        # 交互式模式
        interactive_mode(args.database)
    else:
        # 默认导出所有表
        print("导出所有预定义表")
        export_chatbi_data_only(args.database)

def interactive_mode(database_name):
    """交互式模式"""
    choice = input("\nChoose export option:\n1. All tables\n2. Specific tables\n\nEnter choice (1 or 2): ")

    if choice == "1":
        export_chatbi_data_only(database_name)
    elif choice == "2":
        available_tables = [
            'bad_case', 'chat_history', 'department_top_questions_weekly',
            'good_case', 'share_map', 'user_recommendation_offline',
            'user_session', 'user'
        ]

        print(f"\nAvailable tables:")
        for i, table in enumerate(available_tables, 1):
            print(f"{i}. {table}")

        selection = input("\nEnter table numbers separated by commas (e.g., 1,3,5): ")
        try:
            selected_indices = [int(x.strip()) - 1 for x in selection.split(",")]
            selected_tables = [available_tables[i] for i in selected_indices if 0 <= i < len(available_tables)]

            if selected_tables:
                export_specific_tables(selected_tables, database_name)
            else:
                print("❌ No valid tables selected!")
        except (ValueError, IndexError):
            print("❌ Invalid input! Please enter valid table numbers.")
    else:
        print("❌ Invalid choice!")

if __name__ == "__main__":
    main()