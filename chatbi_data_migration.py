import subprocess
import datetime
import os

def export_chatbi_data_only():
    """Export only data (INSERT statements) from chatbi_test database"""
    
    # Database configuration
    config = {
        'host': 'mysql-xm.summerfarm.net',
        'port': '3308',
        'user': 'test',
        'password': 'xianmu619',
        'database': 'chatbi_test'
    }
    
    # List of tables to export
    tables = [
        'bad_case',
        'chat_history', 
        'department_top_questions_weekly',
        'good_case',
        'share_map',
        'user_recommendation_offline',
        'user_session',
        'user'
    ]
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d")
    output_file = f"chatbi_data_only_{timestamp}.sql"
    
    # Build mysqldump command with data-only options
    cmd = [
        'mysqldump',
        '-h', config['host'],
        '-P', config['port'],
        '-u', config['user'],
        f"-p{config['password']}",
        '--no-create-info',      # Don't include CREATE TABLE statements
        '--skip-add-drop-table', # Don't include DROP TABLE statements
        '--skip-comments',       # Skip comments for cleaner output
        '--compact',             # Produce more compact output
        '--single-transaction',  # Ensure consistent backup
        config['database']
    ] + tables  # Add table names at the end
    
    try:
        print(f"Exporting data from database {config['database']}...")
        print(f"Host: {config['host']}:{config['port']}")
        print(f"Tables: {', '.join(tables)}")
        print("Options: Data only (no CREATE/DROP statements)")
        print("-" * 50)
        
        with open(output_file, 'w') as f:
            # Write header comment
            f.write(f"-- Data export for chatbi_test database\n")
            f.write(f"-- Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"-- Contains INSERT statements only (no CREATE/DROP statements)\n")
            f.write(f"--\n\n")
            
            result = subprocess.run(cmd, stdout=f, stderr=subprocess.PIPE, text=True)
        
        if result.returncode == 0:
            file_size = os.path.getsize(output_file)
            print(f"✅ Export successful!")
            print(f"📁 File: {output_file}")
            print(f"📊 Size: {file_size / 1024:.2f} KB")
            
            # Show preview of file contents
            print("\n📋 File preview (first 10 lines):")
            with open(output_file, 'r') as f:
                for i, line in enumerate(f):
                    if i >= 10:
                        break
                    print(f"   {line.rstrip()}")
            
            print(f"\n💡 To import this data:")
            print(f"   mysql -h your_host -P your_port -u your_user -p your_database < {output_file}")
            
        else:
            print(f"❌ Export failed!")
            print(f"Error: {result.stderr}")
            if os.path.exists(output_file):
                os.remove(output_file)
                
    except FileNotFoundError:
        print("❌ Error: mysqldump command not found!")
        print("Please make sure MySQL client tools are installed.")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def export_specific_tables(table_list):
    """Export data from specific tables only"""
    
    config = {
        'host': 'mysql-xm.summerfarm.net',
        'port': '3308',
        'user': 'test',
        'password': 'xianmu619',
        'database': 'chatbi_test'
    }
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"chatbi_selected_data_{timestamp}.sql"
    
    cmd = [
        'mysqldump',
        '-h', config['host'],
        '-P', config['port'],
        '-u', config['user'],
        f"-p{config['password']}",
        '--no-create-info',
        '--skip-add-drop-table',
        '--skip-comments',
        '--compact',
        '--single-transaction',
        config['database']
    ] + table_list
    
    try:
        print(f"Exporting data from selected tables: {', '.join(table_list)}")
        
        with open(output_file, 'w') as f:
            f.write(f"-- Selected tables data export\n")
            f.write(f"-- Tables: {', '.join(table_list)}\n")
            f.write(f"-- Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            result = subprocess.run(cmd, stdout=f, stderr=subprocess.PIPE, text=True)
        
        if result.returncode == 0:
            file_size = os.path.getsize(output_file)
            print(f"✅ Export successful! File: {output_file} ({file_size / 1024:.2f} KB)")
        else:
            print(f"❌ Export failed: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🚀 ChatBI Data Export Tool")
    print("=" * 40)
    
    choice = input("\nChoose export option:\n1. All tables\n2. Specific tables\n\nEnter choice (1 or 2): ")
    
    if choice == "1":
        export_chatbi_data_only()
    elif choice == "2":
        available_tables = [
            'bad_case', 'chat_history', 'department_top_questions_weekly',
            'good_case', 'share_map', 'user_recommendation_offline',
            'user_session', 'users'
        ]
        
        print(f"\nAvailable tables:")
        for i, table in enumerate(available_tables, 1):
            print(f"{i}. {table}")
        
        selection = input("\nEnter table numbers separated by commas (e.g., 1,3,5): ")
        try:
            selected_indices = [int(x.strip()) - 1 for x in selection.split(",")]
            selected_tables = [available_tables[i] for i in selected_indices if 0 <= i < len(available_tables)]
            
            if selected_tables:
                export_specific_tables(selected_tables)
            else:
                print("❌ No valid tables selected!")
        except (ValueError, IndexError):
            print("❌ Invalid input! Please enter valid table numbers.")
    else:
        print("❌ Invalid choice!")